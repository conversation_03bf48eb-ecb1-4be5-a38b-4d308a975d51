#all
{
margin-left:7%;
width:1180px;
}
.board
{
color:white;
height:50px;
background-color:red;
border-top-style:solid;
border-top-color:purple;
border-bottom-style:inset;
border-bottom-color:purple;
}
.details
{


background-color:orange;
height:50px;
}
.pageviews
{
width:200px;
height:200px;
background-color:orange;
float:left;
border-right-style:solid;
border-right-color:purple;
}
.clicks
{
width:200px;
height:200px;
background-color:orange;
float:left;
border-right-style:solid;
border-right-color:purple;
}
.impression
{
width:204px;
height:200px;
background-color:orange;
float:left;
border-right-style:solid;
border-right-color:purple;
}.credits
{
width:200px;
height:200px;
background-color:orange;
float:left;
border-right-style:solid;
border-right-color:purple;
}.creditsused
{
width:179px;
height:200px;
background-color:orange;
float:left;
border-right-style:solid;
border-right-color:purple;
}.myimpression
{
width:179px;
height:200px;
background-color:orange;
float:left;
border-right-style:solid;
border-right-color:purple;
}
.refer
{
width:613px;
height:280px;
background-color:black;
border-top-style:solid;
border-top-color:purple;
border-right-style:inset;
float:left;
overflow:scroll;
color:white;
}.google
{
width:564px;
height:280px;
background-color:black;
border-top-style:solid;
border-top-color:purple;
float:left;
overflow:scroll;
color:white;
}
.myweb
{
width:610px;

/*background-color:red;*/
float:left;
text-align:center;
border-right-style:solid;
opacity:0.95;

border-right-color:purple;
font-size:35px;
height:50px;
}.myweb2
{
width:610px;
background-color:red;
/*background-color:red;*/
float:left;
text-align:center;
border-right-style:solid;
opacity:0.95;
border-top-style:solid;
border-top-color:purple;
border-bottom-style:solid;
border-bottom-color:purple;
border-right-color:purple;
font-size:35px;
height:50px;
}.credit
{
width:200px;
opacity:0.95;

/*background-color:red;*/
float:left;
text-align:center;
font-size:35px;
border-right-style:solid;
border-right-color:purple;
height:50px;
}.credit2
{
width:200px;
opacity:0.95;
border-top-style:solid;
border-top-color:purple;
border-bottom-style:solid;
border-bottom-color:purple;
background-color:red;
float:left;
text-align:center;
font-size:35px;
border-right-style:solid;
border-right-color:purple;
height:50px;
}.myads
{
width:340px;
opacity:0.95;

/*background-color:red;*/
float:left;
text-align:center;
font-size:35px;
height:50px;
}.myads2
{
width:360px;
opacity:0.95;
border-top-style:solid;
border-top-color:purple;
border-bottom-style:solid;
border-bottom-color:purple;
border-right-color:purple;
border-right-style:solid;
background-color:red;
float:left;
text-align:center;
font-size:35px;
height:50px;
}
img
{
float:left;
}
.username
{

background-color:green;
color:white;
font-size:20px;
float:left;
height:50px;
}
hr.whr
{
padding: 0px;
    margin: 0px;
}
p.points
{
color:white;
font-size:70px;
font-weight:bold;
text-align:center;
height:0px;
margin-top:2%;
cursor:pointer;
}p.points:hover
{
color:red;
font-size:70px;
font-weight:bold;
text-align:center;
height:0px;
margin-top:2%;
cursor:pointer;
}
p.text{
color:black;
font-size:20px;
text-align:center;
}
p.past
{
margin-top:27%;
border-top-style:solid;
border-bottom-style:solid;
border-bottom-color:white;
border-top-color:white;
}p.past2
{
margin-top:-7%;

}p.past3
{
margin-top:31%;
border-top-style:solid;
border-bottom-style:solid;
border-bottom-color:white;
border-top-color:white;
}p.past4
{
margin-top:-7%;

}p.past6
{
margin-top:27.50%;
border-top-style:solid;
border-bottom-style:solid;
border-bottom-color:white;
border-top-color:white;
}p.past5
{
margin-top:-7%;

}
sup.what
{
width:20px;
font-size:15px;
cursor:pointer;

}sup.what:hover
{
width:20px;
font-size:15px;
cursor:pointer;
color:black;
}
acronym
{
font-size:13px;
}
.show_off:hover
{
background-color:purple;
border-bottom-style:solid;
border-bottom-color:white;
border-top-color:white;
border-top-style:solid;
cursor:pointer;
}
.date
{
text-align:center;
font-weight:bold;
font-size:40px;
float:right;
width:220px;
height:50px;
background-color:green;
color:white;
}
.mymenu
{
font-size:25px;
font-weight:bold;
text-align:center;
background-color:orange;
color:white;
width:80px;
height:30px;
float:left;
position:fixed;
margin-left:-10px;
cursor:pointer;
visibility:hidden;
}
#jquery
{
background-color:black;
color:white;
position:fixed;
height:1000px;
width:1500px;
margin-left:-10px;
margin-top:-235px;
opacity:0.50;
visibility:hidden;
}
p.hidensheek{
font-size:20px;
text-align:justify;
margin-top:-2%;
}
.hint
{
font-size:20px;
width:350px;
background-color:red;
float:left;
text-align:center;
border-right-style:inset;
border-right-color:white;
border-bottom-style:inset;
border-bottom-color:white;
margin-left:-350px;
margin-top:-100px;
opacity:0.8;
}
p.hints
{
background-color:green;
margin-top:-6.50%;

}
.close
{
margin-top:0%;
margin-left:93%;
cursor:pointer;
}
.close:hover
{
color:red;
}
.search
{
text-align:center;
background-color:red;
}
.result
{
background-color:black;
border-bottom-style:solid;
border-bottom-color:white;
border-top-color:white;
border-top-style:solid;
cursor:pointer;
text-align:center;
font-style:italic;
font-weight:bold;
}
.result:hover
{
background-color:purple;
}
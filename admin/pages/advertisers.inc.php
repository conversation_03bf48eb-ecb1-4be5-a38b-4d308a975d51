<h1>Advertiser Members</h1>
<?php
session_start();
include 'data.php';

function show_advertisers(){
    global $con;
    
    $query = mysqli_query($con, "SELECT DISTINCT m.username, m.firstname, m.lastname, m.presentdomain, s.date, s.status 
                                 FROM members m 
                                 LEFT JOIN soosal_ads s ON m.username = s.username 
                                 WHERE s.username IS NOT NULL 
                                 ORDER BY s.date DESC");
    
    while ($fetch = mysqli_fetch_assoc($query)) {
        $username = $fetch['username'];
        $firstname = $fetch['firstname'];
        $lastname = $fetch['lastname'];
        $domain = $fetch['presentdomain'];
        $date = $fetch['date'];
        $status = $fetch['status'];
        
        $status_text = '';
        switch($status) {
            case 0: $status_text = 'Pending'; break;
            case 1: $status_text = 'Approved'; break;
            case 2: $status_text = 'Rejected'; break;
            case 3: $status_text = 'Suspended'; break;
            case 4: $status_text = 'Stopped'; break;
            default: $status_text = 'Unknown';
        }
        
        echo "<div class='show_off'>
                <i>Username: $username<br/>
                Name: $firstname $lastname<br/>
                Domain: <a href='http://$domain' target='_blank'>$domain</a><br/>
                Status: $status_text<br/>
                Joined: $date</i>
              </div>
              <a href='home.php?p=advertisers&delete=$username'><u>Delete</u></a>
              <a href='home.php?p=advertisers&suspend=$username'><u>Suspend</u></a>
              <hr/>";
    }
}

// Handle actions
$delete = $_GET['delete'] ?? '';
$suspend = $_GET['suspend'] ?? '';
$error = array();

if ($delete) {
    if (!empty($delete)) {
        mysqli_query($con, "DELETE FROM soosal_ads WHERE username = '$delete'");
        mysqli_query($con, "DELETE FROM members WHERE username = '$delete'");
        $error[0] = "<div class='hint'><div class='close' onclick='popup();'>X</div><p class='hints'>Message</p>
        Advertiser has been successfully deleted!
        </div>";
    }
}

if ($suspend) {
    if (!empty($suspend)) {
        mysqli_query($con, "UPDATE soosal_ads SET status = '3' WHERE username = '$suspend'");
        $error[0] = "<div class='hint'><div class='close' onclick='popup();'>X</div><p class='hints'>Message</p>
        Advertiser has been successfully suspended!
        </div>";
    }
}
?>

<div id="color">
    <div class="google">
        <?php echo show_advertisers(); ?>
    </div>
    <div class="ads">
        <div class="search">
            <form action="<?php echo $_SERVER['REQUEST_URI']; ?>" method="post">
                <b>Search:</b><input type="text" name="search">
                <input type="submit" name="submit">
            </form>
        </div>
        <?php echo $error[0] ?? ''; ?>
    </div>
</div>

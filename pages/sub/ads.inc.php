<?php session_start();

?>
<div class="one">

<div class="infos">
<div class="yeta">
</div>
</div>
<div class="action">
<?PHP 
include 'db.inc.php';
global $con;
$user = $_SESSION['username'];
$query = mysqli_query($con, "SELECT status from soosal_ads WHERE username= '$user'");
$fetch = mysqli_fetch_assoc($query);
$status = $fetch['status'];

switch ($status)
{
case 0:
echo "<div class='uta'></div>";
break;
case 1:
echo "<div class='uta1'></div>";
break;
case 2:
echo "<div class='uta2'></div>";
break;
case 3:
echo "<div class='uta3'></div>";
break;
case 4:
echo "<div class='uta4'></div>";
break;

}

?>

</div>
</div>
<div class="one">

<div class="infos2">
<span class="yeta">
</span>
</div>
<div class="action">
<?PHP 
include 'db.inc.php';
global $con;
$user = $_SESSION['username'];
$query = mysqli_query($con, "SELECT status from soosal_ads WHERE username= '$user'");
$fetch = mysqli_fetch_assoc($query);
$status = $fetch['status'];
if ($status == 1 || $status == 4)
{
switch ($status)
{
case 1:
echo "<form action='' method='post'>
      <input type='submit' name='change' value='' class='ads'>
</form>";
$change = $_POST['change']; 
if (isset($change))
{
mysqli_query($con, "UPDATE soosal_ads SET status = '4' where username  = '$user'");
$query = mysqli_query($con, "select username from approved ads where username = '$user'");
$rows = mysqli_num_rows($query);
if ($rows < 1){
mysqli_query($con, "DELETE FROM approved_ads WHERE username = '$user'");
echo "<div class='hint' ><div class='close' onclick='popup();'>X</div><p class='hints'>Message</p>
Your ads will stopped soon!
</div>";}
else
{
echo "<div class='hint' ><div class='close' onclick='popup();'>X</div><p class='hints'>Message</p>
Your ads are already stopped!
</div>";
}
}

break;

case 4:
echo "<form action='' method='post'>
      <input type='submit' name='changes' value='' class='ads2'>
</form>";
$changes = $_POST['changes']; 
if (isset($changes))
{
mysqli_query($con, "UPDATE soosal_ads SET status = '1' where username  = '$user'");
$insert = mysqli_query($con, "SELECT * FROM soosal_ads WHERE username = '$user'");
$infetch = mysqli_fetch_array($insert);
$insertbanner = $infetch['banner'];
$inserturl = $infetch['url'];
$insertcredits = $infetch['ads_credit'];
$insertid= $infetch['ads_id'];
$date = date("Y-m-d");
$checkquery = mysqli_query($con, "SELECT ads_id from approved_ads WHERE username = '$user'");
$checkrows = mysqli_num_rows($checkquery);
if ($checkrows < 1){
mysqli_query($con, "INSERT INTO approved_ads VALUES ('','$insertid','$insertbanner','$insertcredits','$user','$date','$inserturl')");}
echo "<div class='hint' ><div class='close' onclick='popup();'>X</div><p class='hints'>Message</p>
Your ads will be active soon!
</div>";
}
break;
default:
echo "<form action='' method='post'>
      <input type='submit' name='changess' value='' class='ads3'>
</form>";
$changess = $_POST['changess']; 
if (isset($changess))
{
mysqli_query($con, "UPDATE soosal_ads SET status = '0' where username  = '$user'");
echo "<div class='hint' ><div class='close' onclick='popup();'>X</div><p class='hints'>Message</p>
Your ads request has been sucessfull.we will take review of your website and as soon as it will approved your ads will run sucessfully!
</div>";
}
break;
	  
	  
}	
}
else
{
echo "<form action='' method='post'>
      <input type='submit' name='changess' value='' class='ads3'>
</form>";
$changess = $_POST['changess']; 
if (isset($changess))
{
mysqli_query($con, "UPDATE soosal_ads SET status = '0' where username  = '$user'");
echo "<div class='hint' ><div class='close' onclick='poup();'>X</div><p class='hints'>Message</p>
Your ads request has been sucessfull.we will take review of your website and as soon as it will approved your ads will run sucessfully!
</div>";
}

}  

?>

</div>
</div>
# Adsense Clone - Setup and Testing Guide

## Prerequisites
- <PERSON><PERSON> and <PERSON><PERSON> Compose installed
- External MySQL database container named "appsDB" running
- Database "soosal" created in the MySQL container

## Setup Instructions

### 1. Database Setup
First, ensure your database is set up with the correct schema and test data:

```bash
# Connect to your MySQL container
docker exec -it appsDB mysql -u root -p

# Run the database setup script
mysql -u root -p soosal < database_setup.sql

# Insert test data
mysql -u root -p soosal < insert_test_data.sql
```

### 2. Application Startup
Start the application using Docker Compose:

```bash
# From the project root directory
docker-compose up -d --build
```

The application will be available at: http://localhost:8082

### 3. Test User Accounts
The following test accounts are available:

| Username | Password | Role | Description |
|----------|----------|------|-------------|
| testuser | testpass | User | Regular user with ads |
| admin | admin123 | Admin | Administrator account |
| publisher1 | pub123 | Publisher | Publisher with website |

## Testing Checklist

### ✅ Basic Functionality Tests

#### 1. Login System
- [ ] Visit http://localhost:8082
- [ ] Should redirect to login page
- [ ] Try logging in with `testuser` / `testpass`
- [ ] Should redirect to main dashboard
- [ ] Verify logout functionality works

#### 2. Main Dashboard (index.php)
- [ ] User information displays correctly
- [ ] Statistics show (pageviews, clicks, impressions, credits)
- [ ] Navigation menu works
- [ ] Recent activity displays

#### 3. Navigation Testing
Test all navigation links:
- [ ] AdsZone (index.php) - Main dashboard
- [ ] My ads clicks (home.php?p=my-ads)
- [ ] My page clicks (home.php?p=page-clicks)
- [ ] My ads Impression (home.php?p=ads-impression)
- [ ] My page Impression (home.php?p=page-impression)
- [ ] Visits (home.php?p=visits)
- [ ] Spent credits (home.php?p=credits-spent)
- [ ] Earn credits (home.php?p=credits-earn)
- [ ] Settings (home.php?p=setup)
- [ ] Exit (logout.php)

#### 4. Admin Panel Testing
- [ ] Login as admin user
- [ ] Visit admin panel at http://localhost:8082/admin/
- [ ] Test admin navigation:
  - [ ] Publisher members (home.php?p=publishers)
  - [ ] Advertiser members (home.php?p=advertisers)
  - [ ] Publishers clicks (home.php?p=clicks)
  - [ ] Advertisers clicks (home.php?p=adv)
  - [ ] Publishers ads (home.php?p=ads-impression)
  - [ ] Advertisers ads (home.php?p=page-impression)
  - [ ] Visits (home.php?p=visits)
  - [ ] Credits pages (home.php?p=credits-spent, home.php?p=credits-earn)
  - [ ] Settings (home.php?p=setup)

#### 5. Ad Creation and Management
- [ ] Visit http://localhost:8082/createads/
- [ ] Test ad creation form
- [ ] Verify ads appear in database

#### 6. Publisher Ad Code Generation
- [ ] Visit http://localhost:8082/getads/
- [ ] Test ad code generation
- [ ] Verify iframe code is generated correctly

#### 7. Ad Display System
- [ ] Test ad display at http://localhost:8082/adshow.php?pubid=testuser
- [ ] Verify ads are shown correctly
- [ ] Test click tracking via ads_l.php

### 🔧 Advanced Testing

#### Database Integrity
- [ ] Verify all tables have data
- [ ] Check foreign key relationships
- [ ] Test data consistency

#### Error Handling
- [ ] Test with invalid login credentials
- [ ] Test accessing protected pages without login
- [ ] Test with malformed URLs

#### Performance
- [ ] Check page load times
- [ ] Verify database queries are efficient
- [ ] Test with multiple concurrent users

## Common Issues and Solutions

### Issue: "Failed to connect to MySQL"
**Solution:** Ensure the appsDB container is running and accessible:
```bash
docker ps | grep appsDB
docker network ls | grep rajnitireport_app_network
```

### Issue: "Table doesn't exist"
**Solution:** Run the database setup scripts:
```bash
mysql -u root -p soosal < database_setup.sql
```

### Issue: Login redirects to blank page
**Solution:** Check that BASE_URL is set correctly in config.php and that the database has user data.

### Issue: Navigation links don't work
**Solution:** Verify that all page include files exist in the pages/ directory.

## File Structure Verification

Ensure these key files exist:
```
├── index.php (main dashboard)
├── login.php (authentication)
├── logout.php (session cleanup)
├── home.php (page router)
├── config.php (configuration)
├── db.inc.php (database connection)
├── pages/ (page includes)
├── admin/ (admin panel)
├── createads/ (ad creation)
├── getads/ (publisher tools)
└── images/ (assets)
```

## Security Notes

⚠️ **Important Security Considerations:**
- This is a 2012-era application with known security vulnerabilities
- Passwords are stored in plain text
- SQL injection vulnerabilities exist
- Use only for testing/educational purposes
- Do not expose to public internet without security hardening

## Support

If you encounter issues:
1. Check Docker container logs: `docker-compose logs`
2. Verify database connectivity
3. Check file permissions
4. Review error logs in browser developer tools

## Next Steps

After successful testing:
1. Consider implementing security improvements
2. Add input validation and sanitization
3. Implement password hashing
4. Add CSRF protection
5. Use prepared statements for database queries

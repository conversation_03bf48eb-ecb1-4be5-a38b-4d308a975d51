ol
{
list-style-type:none;

}
#sidebars
{

font-size:20px;
height:22px;
background-image:url(../images/one.jpg);

}
li.setting
{
background-color:red;
text-align:left;
margin-left:-0.01%;
padding-left:0px;
padding-right:0px;
height:20px;
margin-top:2%;
display:inline;
}li.setting:hover
{
background-color:black;
text-align:left;
margin-left:-0.01%;
padding-left:0px;
padding-right:0px;

margin-top:2%;
display:inline;
}
li.setting a:link
{
color:white;
}
li.setting a:visited
{
color:white;
}
.one
{
border-top-style:solid;
border-top-color:white;
border-left-style:solid;
border-left-color:white;
border-right-style:solid;
border-right-color:white;
border-bottom-style:solid;
border-bottom-color:white;
border-radius:50px;
width:900px;
height:70px;
margin-left:5%;
background-image:url(../images/one.jpg);
background-repeat:no-repeat;
opacity:0.90;
margin-top:6px;
}
.infos
{
border-top-left-radius:50px;
border-bottom-left-radius:50px;
border-right-style:solid;
border-right-color:white;
background-image:url(../images/status.jpg);
background-repeat:no-repeat;
color:white;
width:200px;
height:70px;
float:left;
font-size:50px;
text-align:center;
}.infos2
{
border-top-left-radius:50px;
border-bottom-left-radius:50px;
background-image:url(../images/ads.jpg);
background-repeat:no-repeat;
border-right-style:solid;
border-right-color:white;
color:white;
width:200px;
height:70px;
float:left;
font-size:50px;
text-align:center;
}
.action
{
border-top-right-radius:50px;
border-bottom-right-radius:50px;
background-color:orange;
color:white;
width:200px;
height:70px;
float:right;
font-size:50px;
text-align:center;
}
.yeta
{

margin-left:15px;

}
.uta
{
width:200px;
height:70px;
border-top-right-radius:50px;
border-bottom-right-radius:50px;
background-image:url(../images/pending.jpg);
background-repeat:no-repeat;
margin-left:-0%;
border-left-style:solid;
border-left-color:white;
}
.uta1
{
width:200px;
height:70px;
border-top-right-radius:50px;
border-bottom-right-radius:50px;
background-image:url(../images/running.jpg);
background-repeat:no-repeat;
margin-left:-0%;
border-left-style:solid;
border-left-color:white;
}.uta2
{
width:200px;
height:70px;
border-top-right-radius:50px;
border-bottom-right-radius:50px;
background-image:url(../images/rejected.jpg);
background-repeat:no-repeat;
margin-left:-0%;
border-left-style:solid;
border-left-color:white;

}.uta3
{
width:200px;
height:70px;
border-top-right-radius:50px;
border-bottom-right-radius:50px;
background-image:url(../images/suspend.jpg);
background-repeat:no-repeat;
margin-left:-0%;
border-left-style:solid;
border-left-color:white;
}.uta4
{
width:200px;
height:70px;
border-top-right-radius:50px;
border-bottom-right-radius:50px;
background-image:url(../images/stopped.jpg);
background-repeat:no-repeat;
margin-left:-0%;
border-left-style:solid;
border-left-color:white;
}
input.ads
{
border-top-right-radius:50px;
border-bottom-right-radius:50px;
width:200px;
height:70px;
margin-top:0%;
font-size:20px;
color:white;
background-image:url(../images/stop.jpg);
cursor:pointer;
}input.ads2
{
border-top-right-radius:50px;
border-bottom-right-radius:50px;
width:200px;
height:70px;
margin-top:0%;
font-size:20px;
color:white;
background-image:url(../images/run.jpg);
cursor:pointer;
}input.ads2:hover
{
border-top-right-radius:50px;
border-bottom-right-radius:50px;
width:200px;
height:70px;
margin-top:0%;
font-size:20px;
color:white;
background-image:url(../images/run2.jpg);
cursor:pointer;
}
input.ads3
{
border-top-right-radius:50px;
border-bottom-right-radius:50px;
width:200px;
height:70px;
margin-top:0%;
font-size:20px;
color:white;
background-image:url(../images/request.jpg);
cursor:pointer;
}input.ads3:hover
{
border-top-right-radius:50px;
border-bottom-right-radius:50px;
width:200px;
height:70px;
margin-top:0%;
font-size:20px;
color:white;
background-image:url(../images/request2.jpg);
cursor:pointer;
}
input.ads:hover
{
border-top-right-radius:50px;
border-bottom-right-radius:50px;
width:200px;
height:70px;
margin-top:0%;
font-size:20px;
color:white;
background-image:url(../images/stop2.jpg);
cursor:pointer;
}.hint
{
font-size:20px;
width:350px;
background-color:red;
float:left;
text-align:center;
border-right-style:inset;
border-right-color:white;
border-bottom-style:inset;
border-bottom-color:white;
margin-left:-350px;
margin-top:-100px;
opacity:0.8;
}
p.hints
{
background-color:green;
margin-top:-6.50%;
width:206px;
}
.close
{
margin-top:0%;
margin-left:93%;
cursor:pointer;
}
.close:hover
{
color:red;
}

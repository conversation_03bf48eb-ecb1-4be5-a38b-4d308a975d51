<h1>Publisher Clicks Details</h1>
<?php
session_start();
include 'data.php';

function show_publisher_clicks(){
    global $con;
    
    $query = mysqli_query($con, "SELECT oc.username, oc.clicks, oc.website, oc.ip_address, oc.date, oc.time, oc.clicked_from 
                                 FROM only_clicks oc 
                                 ORDER BY oc.date DESC, oc.time DESC 
                                 LIMIT 50");
    
    while ($fetch = mysqli_fetch_assoc($query)) {
        $username = $fetch['username'];
        $clicks = $fetch['clicks'];
        $website = $fetch['website'];
        $ip_address = $fetch['ip_address'];
        $date = $fetch['date'];
        $time = $fetch['time'];
        $clicked_from = $fetch['clicked_from'];
        
        echo "<div class='show_off'>
                <i>Publisher: $username<br/>
                Website: <a href='$website' target='_blank'>$website</a><br/>
                Clicks: $clicks<br/>
                IP Address: $ip_address<br/>
                Clicked From: <a href='$clicked_from' target='_blank'>$clicked_from</a><br/>
                Date: $date at $time</i>
              </div><hr/>";
    }
}

function get_total_clicks(){
    global $con;
    $query = mysqli_query($con, "SELECT SUM(clicks) as total FROM only_clicks");
    $result = mysqli_fetch_assoc($query);
    return $result['total'] ?? 0;
}

function get_today_clicks(){
    global $con;
    $today = date('Y-m-d');
    $query = mysqli_query($con, "SELECT SUM(clicks) as total FROM only_clicks WHERE date = '$today'");
    $result = mysqli_fetch_assoc($query);
    return $result['total'] ?? 0;
}

$total_clicks = get_total_clicks();
$today_clicks = get_today_clicks();
?>

<div id="color">
    <div class="data"><p><?php echo $today_clicks; ?></p></div>
    <div class="hijo"><p>Today's Clicks</p></div>
    <div class="total"><p><?php echo $total_clicks; ?></p></div>
    <div class="google">
        <h3>Recent Publisher Clicks</h3>
        <?php echo show_publisher_clicks(); ?>
    </div>
    <div class="ads">
        <div class="search">
            <form action="<?php echo $_SERVER['REQUEST_URI']; ?>" method="post">
                <b>Search Publisher:</b><input type="text" name="search">
                <input type="submit" name="submit">
            </form>
        </div>
    </div>
</div>

<h1>Advertiser Ads Impression</h1>
<?php
session_start();
include 'data.php';

function show_advertiser_impression(){
    global $con;
    
    $query = mysqli_query($con, "SELECT a.username, a.clicked_of, a.impression, a.pageviews, a.website, a.referer, a.ip_address, a.date, a.time 
                                 FROM adv a 
                                 WHERE a.impression > 0 
                                 ORDER BY a.impression DESC, a.date DESC 
                                 LIMIT 50");
    
    while ($fetch = mysqli_fetch_assoc($query)) {
        $username = $fetch['username'];
        $clicked_of = $fetch['clicked_of'];
        $impression = $fetch['impression'];
        $pageviews = $fetch['pageviews'];
        $website = $fetch['website'];
        $referer = $fetch['referer'];
        $ip_address = $fetch['ip_address'];
        $date = $fetch['date'];
        $time = $fetch['time'];
        
        echo "<div class='show_off'>
                <i>Advertiser: $username<br/>
                Clicked by: $clicked_of<br/>
                Website: <a href='$website' target='_blank'>$website</a><br/>
                Impressions: $impression<br/>
                Page Views: $pageviews<br/>
                IP Address: $ip_address<br/>
                Referer: <a href='$referer' target='_blank'>$referer</a><br/>
                Date: $date at $time</i>
              </div><hr/>";
    }
}

function get_total_advertiser_impressions(){
    global $con;
    $query = mysqli_query($con, "SELECT SUM(impression) as total FROM adv");
    $result = mysqli_fetch_assoc($query);
    return $result['total'] ?? 0;
}

function get_today_advertiser_impressions(){
    global $con;
    $today = date('Y-m-d');
    $query = mysqli_query($con, "SELECT SUM(impression) as total FROM adv WHERE date = '$today'");
    $result = mysqli_fetch_assoc($query);
    return $result['total'] ?? 0;
}

$total_impressions = get_total_advertiser_impressions();
$today_impressions = get_today_advertiser_impressions();
?>

<div id="color">
    <div class="data"><p><?php echo $today_impressions; ?></p></div>
    <div class="hijo"><p>Today's Impressions</p></div>
    <div class="total"><p><?php echo $total_impressions; ?></p></div>
    
    <div class="google">
        <h3>Advertiser Impression Activity</h3>
        <?php echo show_advertiser_impression(); ?>
    </div>
    
    <div class="ads">
        <div class="search">
            <form action="<?php echo $_SERVER['REQUEST_URI']; ?>" method="post">
                <b>Search Advertiser:</b><input type="text" name="search">
                <input type="submit" name="submit">
            </form>
        </div>
    </div>
</div>

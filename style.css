#header
{
background-color:purple;
color:white;
height:60px;
font-size:40px;
text-align:left;
font-family:impact;
font-style:italic;
border-radius:4px;
}
a:link ,a:visited
{
text-decoration:none;
color:green;
}
a:hover  ,a:active
{
color:white;

}
#menu{

height:30px;
margin-top:3px;
padding-bottom:10px;
}
ul
{
list-style-type:none;
background-color:purple;
border-radius:5px;
height:30px;
padding-top:9px;
margin-top:0.30%;
}

#hw
{
background-color:green;
color:white;
text-align:center;
margin-left:-0%;
width:300px;
border-radius:6px;
border-style:inset;
margin-top:-10%;
}
#som
{
background-color:purple;
width:1000px;

color:white;
margin-left:9%;
border-radius:12px;
}
td
{

color:white;

}td.form
{
background-color:white;
color:red;
border-radius:12px;
text-align:center;
font-family:impact;
}
table{
margin-left:25%;
margin-top:-10%;
}
table.login{
margin-left:30%;
margin-top:3%;
}
input
{
font-style:italic;
width:260px;
border-radius:4px;
text-align:center;
}
caption{
background-color:white;
color:purple;
width:370px;
margin-left:2%;
border-radius:4px;
font-family:impact;
}
#error
{
background-color:red;
color:white;
text-align:center;
font-family:impact;
font-size:25px;
}#sucess
{
background-color:green;
color:white;
text-align:center;
font-family:impact;
font-size:25px;
}
h1
{
text-align:center;
font-family:verdana;

}



body
{
background-color:#EAEAEA;
}
table.logs{
margin-left:30%;
margin-top:10%;
}
#updates
{
text-align:left;

}
a:link.updates
{
color:green;
}
#sidebar{
margin-left:45%;
margin-top:-40%;
}
#upd
{

margin-left:35%;
margin-top:-30%;
}

<h1>Website Visits Overview</h1>
<?php
session_start();
include 'data.php';

function show_recent_visits(){
    global $con;
    
    $query = mysqli_query($con, "SELECT a.username, a.website, a.ip_address, a.referer, a.date, a.time, a.pageviews 
                                 FROM adv a 
                                 WHERE a.pageviews > 0 
                                 ORDER BY a.date DESC, a.time DESC 
                                 LIMIT 50");
    
    while ($fetch = mysqli_fetch_assoc($query)) {
        $username = $fetch['username'];
        $website = $fetch['website'];
        $ip_address = $fetch['ip_address'];
        $referer = $fetch['referer'];
        $date = $fetch['date'];
        $time = $fetch['time'];
        $pageviews = $fetch['pageviews'];
        
        echo "<div class='show_off'>
                <i>Visitor: $username<br/>
                Website: <a href='$website' target='_blank'>$website</a><br/>
                IP Address: $ip_address<br/>
                Referer: <a href='$referer' target='_blank'>$referer</a><br/>
                Page Views: $pageviews<br/>
                Date: $date at $time</i>
              </div><hr/>";
    }
}

function get_total_visits(){
    global $con;
    $query = mysqli_query($con, "SELECT SUM(pageviews) as total FROM adv");
    $result = mysqli_fetch_assoc($query);
    return $result['total'] ?? 0;
}

function get_today_visits(){
    global $con;
    $today = date('Y-m-d');
    $query = mysqli_query($con, "SELECT SUM(pageviews) as total FROM adv WHERE date = '$today'");
    $result = mysqli_fetch_assoc($query);
    return $result['total'] ?? 0;
}

function get_unique_visitors(){
    global $con;
    $today = date('Y-m-d');
    $query = mysqli_query($con, "SELECT COUNT(DISTINCT ip_address) as total FROM adv WHERE date = '$today'");
    $result = mysqli_fetch_assoc($query);
    return $result['total'] ?? 0;
}

$total_visits = get_total_visits();
$today_visits = get_today_visits();
$unique_visitors = get_unique_visitors();
?>

<div id="color">
    <div class="data"><p><?php echo $today_visits; ?></p></div>
    <div class="hijo"><p>Today's Visits</p></div>
    <div class="hapta"><p><?php echo $unique_visitors; ?></p></div>
    <div class="maina"><p>Unique Visitors</p></div>
    <div class="total"><p><?php echo $total_visits; ?></p></div>
    
    <div class="google">
        <h3>Recent Website Visits</h3>
        <?php echo show_recent_visits(); ?>
    </div>
    
    <div class="ads">
        <div class="search">
            <form action="<?php echo $_SERVER['REQUEST_URI']; ?>" method="post">
                <b>Search by IP:</b><input type="text" name="search">
                <input type="submit" name="submit">
            </form>
        </div>
    </div>
</div>

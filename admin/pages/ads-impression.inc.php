<h1>Publisher Ads Impression</h1>
<?php
session_start();
include 'data.php';

function show_ads_impression(){
    global $con;
    
    $query = mysqli_query($con, "SELECT s.username, s.banner, s.url, s.impression, s.my_impression, s.clicks, s.my_clicks, s.pageviews, s.ads_credit, s.date, s.status 
                                 FROM soosal_ads s 
                                 ORDER BY s.impression DESC, s.date DESC 
                                 LIMIT 50");
    
    while ($fetch = mysqli_fetch_assoc($query)) {
        $username = $fetch['username'];
        $banner = $fetch['banner'];
        $url = $fetch['url'];
        $impression = $fetch['impression'];
        $my_impression = $fetch['my_impression'];
        $clicks = $fetch['clicks'];
        $my_clicks = $fetch['my_clicks'];
        $pageviews = $fetch['pageviews'];
        $ads_credit = $fetch['ads_credit'];
        $date = $fetch['date'];
        $status = $fetch['status'];
        
        $status_text = '';
        switch($status) {
            case 0: $status_text = 'Pending'; break;
            case 1: $status_text = 'Approved'; break;
            case 2: $status_text = 'Rejected'; break;
            case 3: $status_text = 'Suspended'; break;
            case 4: $status_text = 'Stopped'; break;
            default: $status_text = 'Unknown';
        }
        
        echo "<div class='show_off'>
                <i>Publisher: $username<br/>
                Banner: $banner<br/>
                URL: <a href='$url' target='_blank'>$url</a><br/>
                Impressions: $impression (My: $my_impression)<br/>
                Clicks: $clicks (My: $my_clicks)<br/>
                Page Views: $pageviews<br/>
                Credits: $ads_credit<br/>
                Status: $status_text<br/>
                Date: $date</i>
              </div><hr/>";
    }
}

function get_total_impressions(){
    global $con;
    $query = mysqli_query($con, "SELECT SUM(impression) as total FROM soosal_ads");
    $result = mysqli_fetch_assoc($query);
    return $result['total'] ?? 0;
}

function get_today_impressions(){
    global $con;
    $today = date('Y-m-d');
    $query = mysqli_query($con, "SELECT SUM(impression) as total FROM soosal_ads WHERE date = '$today'");
    $result = mysqli_fetch_assoc($query);
    return $result['total'] ?? 0;
}

$total_impressions = get_total_impressions();
$today_impressions = get_today_impressions();
?>

<div id="color">
    <div class="data"><p><?php echo $today_impressions; ?></p></div>
    <div class="hijo"><p>Today's Impressions</p></div>
    <div class="total"><p><?php echo $total_impressions; ?></p></div>
    
    <div class="google">
        <h3>Publisher Ads Performance</h3>
        <?php echo show_ads_impression(); ?>
    </div>
    
    <div class="ads">
        <div class="search">
            <form action="<?php echo $_SERVER['REQUEST_URI']; ?>" method="post">
                <b>Search Publisher:</b><input type="text" name="search">
                <input type="submit" name="submit">
            </form>
        </div>
    </div>
</div>

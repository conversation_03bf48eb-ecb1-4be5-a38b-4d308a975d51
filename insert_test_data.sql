-- Insert test data for Adsense Clone application
USE `soosal`;

-- Insert test users
INSERT INTO `members` (`username`, `password`, `requestdomain`, `presentdomain`, `firstname`, `lastname`, `pending`, `approved`, `reject`) 
VALUES 
('testuser', 'testpass', 'example.com', 'example.com', 'Test', 'User', 0, 1, 0),
('admin', 'admin123', 'admin.local', 'admin.local', 'Admin', 'User', 0, 1, 0),
('publisher1', 'pub123', 'publisher1.com', 'publisher1.com', 'Publisher', 'One', 0, 1, 0);

-- Insert test publishers
INSERT INTO `publishers` (`username`, `website`, `date`) 
VALUES 
('testuser', 'http://example.com', CURDATE()),
('publisher1', 'http://publisher1.com', CURDATE());

-- Insert test ads
INSERT INTO `soosal_ads` (`banner`, `url`, `impression`, `my_impression`, `clicks`, `my_clicks`, `pageviews`, `ads_credit`, `username`, `date`, `status`, `p_status`) 
VALUES 
('banner1.jpg', 'http://example.com', 100, 50, 10, 5, 200, 100, 'testuser', CURDATE(), 1, 1),
('banner2.jpg', 'http://publisher1.com', 150, 75, 15, 8, 300, 150, 'publisher1', CURDATE(), 1, 1);

-- Insert test ad details
INSERT INTO `ads_details` (`ads_name`, `ads_url`, `ads_type`, `ads_category`, `ads_banner`, `ads_description`, `username`, `date`, `time`, `ip_address`) 
VALUES 
('Test Ad 1', 'http://example.com', 'banner', 'technology', 'banner1.jpg', 'This is a test advertisement', 'testuser', CURDATE(), CURTIME(), '127.0.0.1'),
('Test Ad 2', 'http://publisher1.com', 'banner', 'business', 'banner2.jpg', 'Another test advertisement', 'publisher1', CURDATE(), CURTIME(), '127.0.0.1');

-- Insert approved ads
INSERT INTO `approved_ads` (`ads_id`, `banner`, `ads_credit`, `username`, `date`, `url`) 
VALUES 
(1, 'banner1.jpg', 100, 'testuser', CURDATE(), 'http://example.com'),
(2, 'banner2.jpg', 150, 'publisher1', CURDATE(), 'http://publisher1.com');

-- Insert test advertisement tracking data
INSERT INTO `adv` (`ads_id`, `username`, `clicked_of`, `impression`, `pageviews`, `referer`, `ip_address`, `time`, `date`, `website`) 
VALUES 
(1, 'testuser', 'publisher1', 1, 1, 'http://google.com', '*************', CURTIME(), CURDATE(), 'http://example.com'),
(2, 'publisher1', 'testuser', 1, 1, 'http://facebook.com', '*************', CURTIME(), CURDATE(), 'http://publisher1.com');

-- Insert test click tracking
INSERT INTO `only_clicks` (`ads_id`, `username`, `clicks`, `clicked_from`, `website`, `ip_address`, `time`, `date`) 
VALUES 
(1, 'testuser', 1, 'http://google.com', 'http://example.com', '*************', CURTIME(), CURDATE()),
(2, 'publisher1', 1, 'http://facebook.com', 'http://publisher1.com', '*************', CURTIME(), CURDATE());

-- Insert test credits
INSERT INTO `credits_` (`username`, `credit_earn`, `credit_spent`, `referer_address`, `ip_address`, `date`, `time`, `ads_id`, `whose_ads`) 
VALUES 
('testuser', 50, 100, 'http://google.com', '*************', CURDATE(), CURTIME(), 1, 'publisher1'),
('publisher1', 75, 150, 'http://facebook.com', '*************', CURDATE(), CURTIME(), 2, 'testuser');

-- Insert test earn points
INSERT INTO `earn_points` (`user_id`, `ads_id`, `username`, `earn_points`, `total_points`, `date`, `time`) 
VALUES 
(1, 1, 'testuser', 10, 50, CURDATE(), CURTIME()),
(2, 2, 'publisher1', 15, 75, CURDATE(), CURTIME());

-- Insert test final data
INSERT INTO `final_data` (`username`, `date`, `ads_id`, `pageviews`, `clicks`, `clicks_sent`, `visits`, `credits`, `credits_earned`, `my_ads`) 
VALUES 
('testuser', CURDATE(), 1, 200, 10, 5, 100, 100, 50, 1),
('publisher1', CURDATE(), 2, 300, 15, 8, 150, 150, 75, 1);

-- Insert test admin data
INSERT INTO `admin_data` (`running_ads`, `approved_ads`, `pending_ads`, `rejected_ads`, `suspended_ads`, `stopped_ads`, `publishers`, `advertisers`, `visits`, `clicks`, `impression`, `credits`, `credits_earn`, `credits_spent`, `date`) 
VALUES 
(2, 2, 0, 0, 0, 0, 2, 2, 250, 25, 250, 250, 125, 250, CURDATE());

-- Show inserted data
SELECT 'Test data inserted successfully!' AS Status;
SELECT COUNT(*) AS 'Total Members' FROM members;
SELECT COUNT(*) AS 'Total Ads' FROM soosal_ads;
SELECT COUNT(*) AS 'Total Publishers' FROM publishers;

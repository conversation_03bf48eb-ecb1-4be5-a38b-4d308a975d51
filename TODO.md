# Project: Adsense Clone

## Project Status

- The application has been successfully dockerized.
- It is configured to use an existing external database container named "appsDB".
- The database connection has been updated in all `db.inc.php` files to connect to the "appsDB" container with the database name `soosal`.
- The application is currently running and accessible at [http://localhost:8082](http://localhost:8082).

## Next Steps

The immediate next step is to address the hardcoded URLs in the codebase. The application uses URLs like `/soosal/ads` and `/soosal/login.php`, which will prevent the application from functioning correctly.

When you are ready to continue, you can ask me to fix these hardcoded URLs.

## Commands

- To start the application, run the following command in the project root directory:
  ```bash
  docker-compose up -d --build
  ```

- To stop the application, run the following command:
  ```bash
  docker-compose down
  ```

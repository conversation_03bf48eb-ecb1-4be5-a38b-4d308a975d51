<h1>Credits Spent Overview</h1>
<?php
session_start();
include 'data.php';

function show_credits_spent(){
    global $con;
    
    $query = mysqli_query($con, "SELECT c.username, c.credit_spent, c.whose_ads, c.ip_address, c.date, c.time, c.referer_address 
                                 FROM credits_ c 
                                 WHERE c.credit_spent > 0 
                                 ORDER BY c.date DESC, c.time DESC 
                                 LIMIT 50");
    
    while ($fetch = mysqli_fetch_assoc($query)) {
        $username = $fetch['username'];
        $credit_spent = $fetch['credit_spent'];
        $whose_ads = $fetch['whose_ads'];
        $ip_address = $fetch['ip_address'];
        $date = $fetch['date'];
        $time = $fetch['time'];
        $referer = $fetch['referer_address'];
        
        echo "<div class='show_off'>
                <i>User: $username<br/>
                Credits Spent: $credit_spent<br/>
                On Ads By: $whose_ads<br/>
                IP Address: $ip_address<br/>
                Referer: <a href='$referer' target='_blank'>$referer</a><br/>
                Date: $date at $time</i>
              </div><hr/>";
    }
}

function get_total_credits_spent(){
    global $con;
    $query = mysqli_query($con, "SELECT SUM(credit_spent) as total FROM credits_");
    $result = mysqli_fetch_assoc($query);
    return $result['total'] ?? 0;
}

function get_today_credits_spent(){
    global $con;
    $today = date('Y-m-d');
    $query = mysqli_query($con, "SELECT SUM(credit_spent) as total FROM credits_ WHERE date = '$today'");
    $result = mysqli_fetch_assoc($query);
    return $result['total'] ?? 0;
}

$total_spent = get_total_credits_spent();
$today_spent = get_today_credits_spent();
?>

<div id="color">
    <div class="data"><p><?php echo $today_spent; ?></p></div>
    <div class="hijo"><p>Today's Spent</p></div>
    <div class="total"><p><?php echo $total_spent; ?></p></div>
    
    <div class="google">
        <h3>Recent Credits Spent</h3>
        <?php echo show_credits_spent(); ?>
    </div>
    
    <div class="ads">
        <div class="search">
            <form action="<?php echo $_SERVER['REQUEST_URI']; ?>" method="post">
                <b>Search User:</b><input type="text" name="search">
                <input type="submit" name="submit">
            </form>
        </div>
    </div>
</div>

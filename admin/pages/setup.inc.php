<h1>Admin Settings</h1>
<?php
session_start();
include 'data.php';

// Handle settings updates
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $action = $_POST['action'] ?? '';
    
    if ($action == 'clear_old_data') {
        $days = intval($_POST['days'] ?? 30);
        $cutoff_date = date('Y-m-d', strtotime("-$days days"));
        
        mysqli_query($con, "DELETE FROM adv WHERE date < '$cutoff_date'");
        mysqli_query($con, "DELETE FROM only_clicks WHERE date < '$cutoff_date'");
        mysqli_query($con, "DELETE FROM credits_ WHERE date < '$cutoff_date'");
        
        $message = "Old data (older than $days days) has been cleared successfully!";
    }
    
    if ($action == 'reset_stats') {
        mysqli_query($con, "UPDATE soosal_ads SET impression = 0, my_impression = 0, clicks = 0, my_clicks = 0, pageviews = 0");
        $message = "All statistics have been reset successfully!";
    }
}

function get_database_stats(){
    global $con;
    
    $stats = array();
    
    $tables = ['members', 'soosal_ads', 'adv', 'only_clicks', 'credits_', 'publishers', 'approved_ads'];
    
    foreach ($tables as $table) {
        $query = mysqli_query($con, "SELECT COUNT(*) as count FROM $table");
        $result = mysqli_fetch_assoc($query);
        $stats[$table] = $result['count'];
    }
    
    return $stats;
}

$stats = get_database_stats();
?>

<div id="color">
    <div class="google">
        <h3>Database Statistics</h3>
        <div class="show_off">
            <table border="1" style="width: 100%; color: white;">
                <tr><th>Table</th><th>Records</th></tr>
                <tr><td>Members</td><td><?php echo $stats['members']; ?></td></tr>
                <tr><td>Ads</td><td><?php echo $stats['soosal_ads']; ?></td></tr>
                <tr><td>Ad Views</td><td><?php echo $stats['adv']; ?></td></tr>
                <tr><td>Clicks</td><td><?php echo $stats['only_clicks']; ?></td></tr>
                <tr><td>Credits</td><td><?php echo $stats['credits_']; ?></td></tr>
                <tr><td>Publishers</td><td><?php echo $stats['publishers']; ?></td></tr>
                <tr><td>Approved Ads</td><td><?php echo $stats['approved_ads']; ?></td></tr>
            </table>
        </div>
        
        <?php if (isset($message)): ?>
            <div class="hint">
                <p class="hints"><?php echo $message; ?></p>
            </div>
        <?php endif; ?>
        
        <h3>System Maintenance</h3>
        
        <div class="show_off">
            <h4>Clear Old Data</h4>
            <form method="POST">
                <input type="hidden" name="action" value="clear_old_data">
                <label>Delete data older than: 
                    <select name="days">
                        <option value="30">30 days</option>
                        <option value="60">60 days</option>
                        <option value="90">90 days</option>
                        <option value="180">6 months</option>
                        <option value="365">1 year</option>
                    </select>
                </label>
                <input type="submit" value="Clear Old Data" onclick="return confirm('Are you sure you want to delete old data? This cannot be undone!')">
            </form>
        </div>
        
        <div class="show_off">
            <h4>Reset Statistics</h4>
            <form method="POST">
                <input type="hidden" name="action" value="reset_stats">
                <p>This will reset all impression, click, and pageview counters to zero.</p>
                <input type="submit" value="Reset All Statistics" onclick="return confirm('Are you sure you want to reset all statistics? This cannot be undone!')">
            </form>
        </div>
        
        <div class="show_off">
            <h4>System Information</h4>
            <p><strong>PHP Version:</strong> <?php echo phpversion(); ?></p>
            <p><strong>MySQL Version:</strong> <?php echo mysqli_get_server_info($con); ?></p>
            <p><strong>Server Time:</strong> <?php echo date('Y-m-d H:i:s'); ?></p>
            <p><strong>Application Version:</strong> Adsense Clone v1.0 (2012)</p>
        </div>
    </div>
</div>

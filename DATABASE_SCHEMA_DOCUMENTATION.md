# Adsense Clone Database Schema Documentation

## Overview
This document describes the complete database schema for the Adsense Clone project (circa 2012). The database name is `soosal` and contains 11 tables that manage users, advertisements, clicks, credits, and analytics.

## Database Tables

### 1. `members` - User Management
**Purpose**: Stores user account information
- `id` (int, AUTO_INCREMENT, PRIMARY KEY)
- `username` (varchar 255) - Unique user identifier
- `password` (varchar 255) - User password (plain text in original)
- `requestdomain` (varchar 255) - Requested domain during registration
- `presentdomain` (varchar 255) - Current domain
- `firstname` (varchar 255) - User's first name
- `lastname` (varchar 255) - User's last name
- `pending` (int) - Number of pending requests
- `approved` (int) - Number of approved requests
- `reject` (int) - Number of rejected requests

### 2. `soosal_ads` - Main Advertisement Table
**Purpose**: Core table for managing advertisements
- `ads_id` (int, AUTO_INCREMENT, PRIMARY KEY)
- `banner` (varchar 255) - Banner image filename
- `url` (varchar 255) - Target URL for the ad
- `impression` (int) - Total impressions
- `my_impression` (int) - User's impressions
- `clicks` (int) - Total clicks
- `my_clicks` (int) - User's clicks
- `pageviews` (int) - Page views
- `ads_credit` (int) - Credits allocated for this ad
- `username` (varchar 255) - Ad owner
- `date` (date) - Creation date
- `status` (int) - Ad status (0=pending, 1=approved, 2=rejected, 3=suspended, 4=stopped)
- `p_status` (int) - Publisher status

### 3. `adv` - Advertisement Tracking
**Purpose**: Tracks individual ad views and interactions
- `id` (int, AUTO_INCREMENT, PRIMARY KEY)
- `ads_id` (int) - Reference to soosal_ads
- `username` (varchar 255) - Viewer username
- `clicked_of` (varchar 255) - Who was clicked
- `impression` (int) - Impression count
- `pageviews` (int) - Page view count
- `referer` (varchar 255) - Referring URL
- `ip_address` (varchar 255) - Visitor IP
- `time` (time) - Time of visit
- `date` (date) - Date of visit
- `website` (varchar 255) - Website visited

### 4. `publishers` - Publisher Management
**Purpose**: Manages website publishers
- `id` (int, AUTO_INCREMENT, PRIMARY KEY)
- `username` (varchar 255) - Publisher username
- `website` (varchar 255) - Publisher's website URL
- `date` (date) - Registration date

### 5. `ads_details` - Advertisement Details
**Purpose**: Stores detailed ad information during creation
- `id` (int, AUTO_INCREMENT, PRIMARY KEY)
- `ads_name` (varchar 255) - Advertisement name
- `ads_url` (varchar 255) - Target URL
- `ads_type` (varchar 255) - Type of advertisement
- `ads_category` (varchar 255) - Category
- `ads_banner` (varchar 255) - Banner filename
- `ads_description` (text) - Description
- `username` (varchar 255) - Creator
- `date` (date) - Creation date
- `time` (time) - Creation time
- `ip_address` (varchar 255) - Creator's IP

### 6. `approved_ads` - Approved Advertisements
**Purpose**: Tracks approved ads ready for display
- `id` (int, AUTO_INCREMENT, PRIMARY KEY)
- `ads_id` (int) - Reference to main ad
- `banner` (varchar 255) - Banner image
- `ads_credit` (int) - Credits allocated
- `username` (varchar 255) - Ad owner
- `date` (date) - Approval date
- `url` (varchar 255) - Target URL

### 7. `only_clicks` - Click Tracking
**Purpose**: Detailed click tracking
- `id` (int, AUTO_INCREMENT, PRIMARY KEY)
- `ads_id` (int) - Advertisement ID
- `username` (varchar 255) - Clicker username
- `clicks` (int) - Number of clicks
- `clicked_from` (varchar 255) - Source of click
- `website` (varchar 255) - Website where click occurred
- `ip_address` (varchar 255) - Clicker's IP
- `time` (time) - Click time
- `date` (date) - Click date

### 8. `credits_` - Credit System
**Purpose**: Manages the credit/payment system
- `id` (int, AUTO_INCREMENT, PRIMARY KEY)
- `username` (varchar 255) - User
- `credit_earn` (int) - Credits earned
- `credit_spent` (int) - Credits spent
- `referer_address` (varchar 255) - Referring address
- `ip_address` (varchar 255) - User's IP
- `date` (date) - Transaction date
- `time` (time) - Transaction time
- `ads_id` (int) - Related advertisement
- `whose_ads` (varchar 255) - Ad owner

### 9. `earn_points` - Points System
**Purpose**: Alternative points/rewards system
- `id` (int, AUTO_INCREMENT, PRIMARY KEY)
- `user_id` (int) - User reference
- `ads_id` (int) - Advertisement reference
- `username` (varchar 255) - User
- `earn_points` (int) - Points earned
- `total_points` (int) - Total accumulated points
- `date` (date) - Earning date
- `time` (time) - Earning time

### 10. `final_data` - Daily Aggregation
**Purpose**: Daily summary data for users
- `id` (int, AUTO_INCREMENT, PRIMARY KEY)
- `username` (varchar 255) - User
- `date` (date) - Summary date
- `ads_id` (int) - Advertisement reference
- `pageviews` (int) - Daily page views
- `clicks` (int) - Daily clicks
- `clicks_sent` (int) - Clicks sent
- `visits` (int) - Daily visits
- `credits` (int) - Daily credits spent
- `credits_earned` (int) - Daily credits earned
- `my_ads` (int) - User's ads shown

### 11. `admin_data` - Administrative Analytics
**Purpose**: System-wide daily statistics for admin dashboard
- `id` (int, AUTO_INCREMENT, PRIMARY KEY)
- `running_ads` (int) - Number of running ads
- `approved_ads` (int) - Approved ads count
- `pending_ads` (int) - Pending ads count
- `rejected_ads` (int) - Rejected ads count
- `suspended_ads` (int) - Suspended ads count
- `stopped_ads` (int) - Stopped ads count
- `publishers` (int) - Active publishers
- `advertisers` (int) - Active advertisers
- `visits` (int) - Total visits
- `clicks` (int) - Total clicks
- `impression` (int) - Total impressions
- `credits` (int) - Total credits in system
- `credits_earn` (int) - Credits earned today
- `credits_spent` (int) - Credits spent today
- `date` (date) - Statistics date

## Key Relationships

1. **Users to Ads**: `members.username` → `soosal_ads.username`
2. **Ads to Tracking**: `soosal_ads.ads_id` → `adv.ads_id`
3. **Users to Publishers**: `members.username` → `publishers.username`
4. **Ads to Approval**: `soosal_ads.ads_id` → `approved_ads.ads_id`
5. **Users to Credits**: `members.username` → `credits_.username`

## Status Codes

### Advertisement Status (`soosal_ads.status`)
- `0` = Pending review
- `1` = Approved and running
- `2` = Rejected
- `3` = Suspended
- `4` = Stopped

### Publisher Status (`soosal_ads.p_status`)
- `1` = Active publisher
- `3` = Suspended publisher

## Setup Instructions

1. Run the `database_setup.sql` script to create the complete database
2. The script will create the database `soosal` and all required tables
3. Optionally uncomment the sample data inserts for testing
4. Ensure your application's database connection points to the `soosal` database

## Security Notes

⚠️ **Important**: This is a 2012-era application with several security issues:
- Passwords are stored in plain text
- SQL queries are vulnerable to injection
- No input sanitization in many places

For production use, consider:
- Implementing password hashing (bcrypt/argon2)
- Using prepared statements
- Adding input validation and sanitization
- Implementing proper session management

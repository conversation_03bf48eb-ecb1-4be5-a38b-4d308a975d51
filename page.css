.data
{
background-image:url(../images/today.jpg);
background-repeat:no-repeat;
width:200px;
height:40px;
margin-top:3px;
line-height:40px;
font-size:20px;
float:left;

}
.hijo
{
background-image:url(../images/yesterday.jpg);
background-repeat:no-repeat;
width:200px;
height:40px;
margin-top:3px;
line-height:40px;
font-size:20px;
float:left;
}.hapta
{
background-image:url(../images/week.jpg);
background-repeat:no-repeat;
width:200px;
height:40px;
margin-top:3px;
line-height:40px;
font-size:20px;
float:left;
}.maina
{
background-image:url(../images/month.jpg);
background-repeat:no-repeat;
width:200px;
height:40px;
margin-top:3px;
line-height:40px;
font-size:20px;
float:left;
}.total
{
background-image:url(../images/total.jpg);
background-repeat:no-repeat;
width:200px;
height:40px;
margin-top:3px;
line-height:40px;
font-size:20px;
float:left;
}
p
{
width:50px;
font-size:20px;
font-weight:bold;
margin-top:-0px;
margin-left:145px;
background-color:red;
height:40px;
text-align:center;
}
p.mc
{
width:50px;
font-size:20px;
font-weight:bold;
margin-top:-0px;
margin-left:145px;
background-color:red;
height:40px;
text-align:center;
}
.google
{
width:564px;
height:280px;
background-color:black;
border-top-style:solid;
border-top-color:purple;
border-left-style:solid;
border-left-color:purple;
float:left;
overflow:scroll;
color:white;
}
.ads
{
width:430;
height:280px;
border-top-style:solid;
border-top-color:purple;
background-color:orange;
float:left;
}
#color
{
margin-top:-20px;
background-color:purple;
}
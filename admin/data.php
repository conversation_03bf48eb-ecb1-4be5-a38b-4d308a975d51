<?php 
session_start();
include 'db.inc.php'; 
$user = $_SESSION['username']; 
$today = date("Y-m-d");
$date = new DateTime("$today");
$date->modify("-1 day");
$yesterday = $date->format("Y-m-d");

/*-------------------FIRST ROW FUNCTIONS HOMEPAGE------------------*/
function visit(){
global $con;
$visit_query = mysqli_query($con, "SELECT sum(pageviews) FROM soosal_ads");
$visits = mysqli_fetch_array($visit_query)[0];
return $visits;
}

function clicks()
{
global $con;
$query = mysqli_query($con, "SELECT sum(my_clicks) FROM soosal_ads");
$clicks = mysqli_fetch_array($query)[0];
return $clicks;
}

function ads_clicks()
{
global $con;
$query = mysqli_query($con, "SELECT sum(clicks) FROM soosal_ads");
$clicks = mysqli_fetch_array($query)[0];
return $clicks;
}
function my_ads_impression()
{
global $con;
$query = mysqli_query($con, "SELECT sum(impression) FROM soosal_ads");
$show_me = mysqli_fetch_array($query)[0];
return $show_me;
}
function my_impression()
{
global $con;
$query = mysqli_query($con, "SELECT sum(my_impression) FROM soosal_ads");
$show_me = mysqli_fetch_array($query)[0];
return $show_me;
}
function total_credits()
{
global $con;
$query = mysqli_query($con, "SELECT sum(ads_credit) FROM soosal_ads");
$show_me = mysqli_fetch_array($query)[0];
return $show_me;
}
/*---------SECOND ROW FUNCTIONS---------------*/
function pending()
{
global $con;
global $today;

$query = mysqli_query($con, "SELECT count(status) FROM soosal_ads WHERE status = '0' AND date = '$today'");
$show_me = mysqli_fetch_array($query)[0];
return $show_me;
}
function pending_y()
{
global $con;
global $yesterday;

$query = mysqli_query($con, "SELECT count(status) FROM soosal_ads WHERE status = '0' AND date = '$yesterday'");
$show_me = mysqli_fetch_array($query)[0];
return $show_me;
}
function pending_t()
{
global $con;
global $yesterday;

$query = mysqli_query($con, "SELECT count(status) FROM soosal_ads WHERE status = '0'");
$show_me = mysqli_fetch_array($query)[0];
return $show_me;
}
function approved()
{
global $con;
global $today;

$query = mysqli_query($con, "SELECT count(status) FROM soosal_ads WHERE status = '1' AND date = '$today'");
$show_me = mysqli_fetch_array($query)[0];
return $show_me;
}
function approved_y()
{
global $con;
global $yesterday;

$query = mysqli_query($con, "SELECT count(status) FROM soosal_ads WHERE status = '1' AND date = '$yesterday'");
$show_me = mysqli_fetch_array($query)[0];
return $show_me;
}
function approved_t()
{
global $con;


$query = mysqli_query($con, "SELECT count(status) FROM soosal_ads WHERE status = '1'");
$show_me = mysqli_fetch_array($query)[0];
return $show_me;
}
function rejected()
{
global $con;
global $today;

$query = mysqli_query($con, "SELECT count(status) FROM soosal_ads WHERE status = '2' AND date = '$today'");
$show_me = mysqli_fetch_array($query)[0];
return $show_me;
}
function rejected_y()
{
global $con;
global $yesterday;

$query = mysqli_query($con, "SELECT count(status) FROM soosal_ads WHERE status = '2' AND date = '$yesterday'");
$show_me = mysqli_fetch_array($query)[0];
return $show_me;
}
function rejected_t()
{
global $con;


$query = mysqli_query($con, "SELECT count(status) FROM soosal_ads WHERE status = '2'");
$show_me = mysqli_fetch_array($query)[0];
return $show_me;
}
function suspended()
{
global $con;
global $today;

$query = mysqli_query($con, "SELECT count(status) FROM soosal_ads WHERE status = '3' AND date = '$today'");
$show_me = mysqli_fetch_array($query)[0];
return $show_me;
}
function suspended_y()
{
global $con;
global $yesterday;

$query = mysqli_query($con, "SELECT count(status) FROM soosal_ads WHERE status = '3' AND date = '$yesterday'");
$show_me = mysqli_fetch_array($query)[0];
return $show_me;
}
function suspended_t()
{
global $con;


$query = mysqli_query($con, "SELECT count(status) FROM soosal_ads WHERE status = '3'");
$show_me = mysqli_fetch_array($query)[0];
return $show_me;
}
function stopped()
{
global $con;
global $today;

$query = mysqli_query($con, "SELECT count(status) FROM soosal_ads WHERE status = '4' AND date = '$today'");
$show_me = mysqli_fetch_array($query)[0];
return $show_me;
}
function stopped_y()
{
global $con;
global $yesterday;

$query = mysqli_query($con, "SELECT count(status) FROM soosal_ads WHERE status = '4' AND date = '$yesterday'");
$show_me = mysqli_fetch_array($query)[0];
return $show_me;
}
function stopped_t()
{
global $con;


$query = mysqli_query($con, "SELECT count(status) FROM soosal_ads WHERE status = '4'");
$show_me = mysqli_fetch_array($query)[0];
return $show_me;
}
###########################################

###########################################
function running()
{
global $con;
$query = mysqli_query($con, "SELECT count(id) FROM approved_ads");
$show_me = mysqli_fetch_array($query)[0];
return $show_me;
}
function running_y()
{
global $con;
global $yesterday;
$query = mysqli_query($con, "SELECT running_ads FROM admin_data WHERE date = '$yesterday'");
$show_me = mysqli_fetch_array($query)[0];
return $show_me;
}function running_t()
{
global $con;
$query = mysqli_query($con, "SELECT sum(running_ads) FROM admin_data");
$show_me = mysqli_fetch_array($query)[0];
return $show_me;
}

function advertisers()
{
global $con;
global $today;
$query = mysqli_query($con, "SELECT count(username) FROM soosal_ads WHERE status = '1' AND date = '$today'");
$show_me = mysqli_fetch_array($query)[0];
return $show_me;
}
function advertisers_y()
{
global $con;
global $yesterday;
$query = mysqli_query($con, "SELECT count(username) FROM soosal_ads WHERE status = '1' AND date = '$yesterday'");
$show_me = mysqli_fetch_array($query)[0];
return $show_me;
}function advertisers_t()
{
global $con;
global $today;
$query = mysqli_query($con, "SELECT count(username) FROM soosal_ads WHERE status = '1'");
$show_me = mysqli_fetch_array($query)[0];
return $show_me;
}
function publishers()
{
global $con;
global $today;
$query = mysqli_query($con, "SELECT count(username) FROM soosal_ads WHERE p_status = '1' AND date = '$today'");
$show_me = mysqli_fetch_array($query)[0];
return $show_me;
}
function publishers_y()
{
global $con;
global $yesterday;
$query = mysqli_query($con, "SELECT count(username) FROM soosal_ads WHERE p_status = '1' AND date = '$yesterday'");
$show_me = mysqli_fetch_array($query)[0];
return $show_me;
}function publishers_t()
{
global $con;
global $today;
$query = mysqli_query($con, "SELECT count(username) FROM soosal_ads WHERE p_status = '1'");
$show_me = mysqli_fetch_array($query)[0];
return $show_me;
}

function show_publisher(){
global $con;

$query  = mysqli_query($con, "SELECT * FROM publishers ORDER BY date desc");
while ($fetch  = mysqli_fetch_assoc($query))
{
$username = $fetch['username'];
$website = $fetch['website'];
$date = $fetch['date'];
echo "<div class='show_off'><i>Username: $username 
<br/> Website: <a href='$website' target='_blank'>$website</a> 
<br/>Joined On: $date</i></div>
<a href='home.php?p=publishers&&delete=$username'><u>Delete</u></a>
<a href='home.php?p=publishers&&suspend=$username'><u>Suspend</u></a> 
<a href='home.php?p=publishers&&stop=$username'><u>Stop</u></a> 
<hr/>";
}
$delete = $_GET['delete'];
$suspend = $_GET['suspend'];
$stop = $_GET['stop'];
$error = array();
if ($delete){
if (!empty($delete))
{
mysqli_query($con, "DELETE FROM soosal_ads WHERE username = '$delete'");
mysqli_query($con, "DELETE FROM publishers WHERE username = '$delete'");
$error[0] = "<div class='hint' ><div class='close' onclick='popup();'>X</div><p class='hints'>Message</p>
User has been sucesfully deleted!
</div>";
}}
if ($suspend)
{
if (!empty($suspend))
{
mysqli_query($con, "UPDATE soosal_ads SET p_status = '3' WHERE username = '$suspend'");
$error[0] = "<div class='hint' ><div class='close' onclick='popup();'>X</div><p class='hints'>Message</p>
User has been sucesfully suspended!
</div>";
}
}
if ($stop)
{
if (!empty($stop))
{
mysqli_query($con, "UPDATE soosal_ads SET p_status = '4' WHERE username = '$stop'");
$error[0] = "<div class='hint' ><div class='close' onclick='popup();'>X</div><p class='hints'>Message</p>
User has been sucesfully stopped!
</div>";
}
}
}

function result()
{
global $con;
$submit = $_POST['submit'];
$search = strip_tags(htmlentities(trim($_POST['search'])));
if (isset($submit))
{
 if (!empty($search))
 {
 if(strlen($search)>6||strlen($search)<30){
 $query = mysqli_query($con, "SELECT * FROM publishers WHERE username LIKE '%$search%' LIMIT 1");
 $rows = mysqli_num_rows($query);
 $fetch = mysqli_fetch_array($query);
 $username = $fetch['username'];
 $website = $fetch['website'];
 $date = $fetch['date'];
if  ($rows < 1)
{
echo "<div class='hint' ><div class='close' onclick='popup();'>X</div><p class='hints'>Message</p>
The user you are searching has not found!
Please check username!
</div>";
}
else
{

echo "<div class='result'>Username: $username<br/>Website: <a href='$website'>$website</a><br/>Joined date: $date<hr/>";
echo "<a href='home.php?p=publishers&&delete=$search'>Delete</a> 
<a href='home.php?p=publishers&&suspend=$search'>Suspend</a>
<a href='home.php?p=publishers&&stop=$search'>Stop!</a></div>";
}
}
else
{
echo "<div class='hint'><div class='close' onclick='popup();'>X</div><p class='hints'>Message</p>
Username under 30 and above 6 characters can only found!
please try again!
</div>";
}
}
else
{echo "<div class='hint' ><div class='close' onclick='popup();'>X</div><p class='hints'>Message</p>
Empty search willl never give any result!
</div>";}
}
}
?>
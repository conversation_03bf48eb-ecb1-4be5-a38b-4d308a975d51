-- Adsense Clone Database Setup Script
-- This script recreates the complete database schema for the Adsense Clone project
-- Database: soosal

-- Create database if it doesn't exist
CREATE DATABASE IF NOT EXISTS `soosal` DEFAULT CHARACTER SET latin1 COLLATE latin1_swedish_ci;
USE `soosal`;

-- Drop tables if they exist (in correct order to handle foreign key constraints)
DROP TABLE IF EXISTS `admin_data`;
DROP TABLE IF EXISTS `adv`;
DROP TABLE IF EXISTS `ads_details`;
DROP TABLE IF EXISTS `approved_ads`;
DROP TABLE IF EXISTS `credits_`;
DROP TABLE IF EXISTS `earn_points`;
DROP TABLE IF EXISTS `final_data`;
DROP TABLE IF EXISTS `members`;
DROP TABLE IF EXISTS `only_clicks`;
DROP TABLE IF EXISTS `publishers`;
DROP TABLE IF EXISTS `soosal_ads`;

-- Create tables

-- Admin data table for storing daily statistics
CREATE TABLE `admin_data` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `running_ads` int(11) DEFAULT NULL,
  `approved_ads` int(11) DEFAULT NULL,
  `pending_ads` int(11) DEFAULT NULL,
  `rejected_ads` int(11) DEFAULT NULL,
  `suspended_ads` int(11) DEFAULT NULL,
  `stopped_ads` int(11) DEFAULT NULL,
  `publishers` int(11) DEFAULT NULL,
  `advertisers` int(11) DEFAULT NULL,
  `visits` int(11) DEFAULT NULL,
  `clicks` int(11) DEFAULT NULL,
  `impression` int(11) DEFAULT NULL,
  `credits` int(11) DEFAULT NULL,
  `credits_earn` int(11) DEFAULT NULL,
  `credits_spent` int(11) DEFAULT NULL,
  `date` date DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;

-- Advertisement tracking table
CREATE TABLE `adv` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `ads_id` int(11) DEFAULT NULL,
  `username` varchar(255) DEFAULT NULL,
  `clicked_of` varchar(255) DEFAULT NULL,
  `impression` int(11) DEFAULT NULL,
  `pageviews` int(11) DEFAULT NULL,
  `referer` varchar(255) DEFAULT NULL,
  `ip_address` varchar(255) DEFAULT NULL,
  `time` time DEFAULT NULL,
  `date` date DEFAULT NULL,
  `website` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;

-- Advertisement details table
CREATE TABLE `ads_details` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `ads_name` varchar(255) DEFAULT NULL,
  `ads_url` varchar(255) DEFAULT NULL,
  `ads_type` varchar(255) DEFAULT NULL,
  `ads_category` varchar(255) DEFAULT NULL,
  `ads_banner` varchar(255) DEFAULT NULL,
  `ads_description` text,
  `username` varchar(255) DEFAULT NULL,
  `date` date DEFAULT NULL,
  `time` time DEFAULT NULL,
  `ip_address` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;

-- Approved advertisements table
CREATE TABLE `approved_ads` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `ads_id` int(11) DEFAULT NULL,
  `banner` varchar(255) DEFAULT NULL,
  `ads_credit` int(11) DEFAULT NULL,
  `username` varchar(255) DEFAULT NULL,
  `date` date DEFAULT NULL,
  `url` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;

-- Credits tracking table
CREATE TABLE `credits_` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `username` varchar(255) DEFAULT NULL,
  `credit_earn` int(11) DEFAULT NULL,
  `credit_spent` int(11) DEFAULT NULL,
  `referer_address` varchar(255) DEFAULT NULL,
  `ip_address` varchar(255) DEFAULT NULL,
  `date` date DEFAULT NULL,
  `time` time DEFAULT NULL,
  `ads_id` int(11) DEFAULT NULL,
  `whose_ads` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;

-- Earn points table
CREATE TABLE `earn_points` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) DEFAULT NULL,
  `ads_id` int(11) DEFAULT NULL,
  `username` varchar(255) DEFAULT NULL,
  `earn_points` int(11) DEFAULT NULL,
  `total_points` int(11) DEFAULT NULL,
  `date` date DEFAULT NULL,
  `time` time DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;

-- Final data aggregation table
CREATE TABLE `final_data` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `username` varchar(255) DEFAULT NULL,
  `date` date DEFAULT NULL,
  `ads_id` int(11) DEFAULT NULL,
  `pageviews` int(11) DEFAULT NULL,
  `clicks` int(11) DEFAULT NULL,
  `clicks_sent` int(11) DEFAULT NULL,
  `visits` int(11) DEFAULT NULL,
  `credits` int(11) DEFAULT NULL,
  `credits_earned` int(11) DEFAULT NULL,
  `my_ads` int(11) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;

-- Members/Users table
CREATE TABLE `members` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `username` varchar(255) DEFAULT NULL,
  `password` varchar(255) DEFAULT NULL,
  `requestdomain` varchar(255) DEFAULT NULL,
  `presentdomain` varchar(255) DEFAULT NULL,
  `lastname` varchar(255) DEFAULT NULL,
  `firstname` varchar(255) DEFAULT NULL,
  `pending` int(11) DEFAULT NULL,
  `approved` int(11) DEFAULT NULL,
  `reject` int(11) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;

-- Click tracking table
CREATE TABLE `only_clicks` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `ads_id` int(11) DEFAULT NULL,
  `username` varchar(255) DEFAULT NULL,
  `clicks` int(11) DEFAULT NULL,
  `clicked_from` varchar(255) DEFAULT NULL,
  `website` varchar(255) DEFAULT NULL,
  `ip_address` varchar(255) DEFAULT NULL,
  `time` time DEFAULT NULL,
  `date` date DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;

-- Publishers table
CREATE TABLE `publishers` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `username` varchar(255) DEFAULT NULL,
  `website` varchar(255) DEFAULT NULL,
  `date` date DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;

-- Main advertisements table
CREATE TABLE `soosal_ads` (
  `ads_id` int(11) NOT NULL AUTO_INCREMENT,
  `banner` varchar(255) DEFAULT NULL,
  `url` varchar(255) DEFAULT NULL,
  `impression` int(11) DEFAULT NULL,
  `my_impression` int(11) DEFAULT NULL,
  `clicks` int(11) DEFAULT NULL,
  `my_clicks` int(11) DEFAULT NULL,
  `pageviews` int(11) DEFAULT NULL,
  `ads_credit` int(11) DEFAULT NULL,
  `username` varchar(255) DEFAULT NULL,
  `date` date DEFAULT NULL,
  `status` int(11) DEFAULT NULL,
  `p_status` int(11) DEFAULT NULL,
  PRIMARY KEY (`ads_id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;

-- Insert sample data for testing (optional)
-- You can uncomment these lines to add test data

-- INSERT INTO `members` (`username`, `password`, `presentdomain`, `firstname`, `lastname`) 
-- VALUES ('testuser', 'testpass', 'example.com', 'Test', 'User');

-- INSERT INTO `publishers` (`username`, `website`, `date`) 
-- VALUES ('testuser', 'http://example.com', CURDATE());

-- INSERT INTO `soosal_ads` (`banner`, `url`, `ads_credit`, `username`, `date`, `status`, `p_status`) 
-- VALUES ('banner.jpg', 'http://example.com', 50, 'testuser', CURDATE(), 1, 1);

-- Show tables to verify creation
SHOW TABLES;

-- Display table structures
DESCRIBE `members`;
DESCRIBE `soosal_ads`;
DESCRIBE `adv`;
DESCRIBE `publishers`;

-- Success message
SELECT 'Database setup completed successfully!' AS Status;

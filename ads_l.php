<?php 
$url = $_GET['li'];
$ads_id = $_GET['id'];
$time = $_GET['ti'];
$referer = $_GET['ref'];
$pub  = $_GET['pubid'];


include 'db.inc.php';
global $con;
$search = mysqli_query($con, "SELECT ads_id FROM approved_ads");
$rows = mysqli_num_rows($search);
if ($rows < 1)
{
header ("Location: index.php");
}else{
$query = mysqli_query($con, "SELECT * FROM soosal_ads WHERE ads_id = '$ads_id'");
$fetch = mysqli_fetch_array($query);
$ads_id_ = $fetch['ads_id'];
$url_ = $fetch['url'];
$banner = $fetch['banner'];
$impression = $fetch['impression'];
$clicks = $fetch['clicks'];
$pageviews = $fetch['pageviews'];
$ads_credit = $fetch['ads_credit'];
$username = $fetch['username'];
$date = $fetch['date'];
$u = md5($url);
$f = sha1($banner);
$time = date("g:i:sa");
$referer_ = $_SERVER['HTTP_REFERER'];
$ip_address = $_SERVER['REMOTE_ADDR'];
$dates = date("Y-m-d");
$iquery = mysqli_query($con, "SELECT * FROM soosal_ads  WHERE username = '$pub'");
$ifetch = mysqli_fetch_assoc($iquery);
$my_clicks = $ifetch['my_clicks'];
$my_credit = $ifetch['ads_credit'];

$avoid_query = mysqli_query($con, "SELECT ip_address FROM credits_ WHERE username = '$pub' AND ip_address = '$ip_address' AND date = '$dates'");
$avoid_rows = mysqli_num_rows($avoid_query);
echo "$avoid_rows";

if ($referer_){
                                                // clicked ads,clicked ads username,clicked on website,clicked ip,clicked date','clciked'time 
mysqli_query($con, "INSERT INTO only_clicks VALUES('','$ads_id','$username','1','$pub','$referer','$ip_address','$time','$dates')");

if ($avoid_rows < 3){
//echo "less then three";
mysqli_query($con, "UPDATE soosal_ads SET ads_credit = '$ads_credit' - 1 , clicks = '$clicks' +1   WHERE ads_id = '$ads_id'");
mysqli_query($con, "UPDATE soosal_ads SET ads_credit = '$my_credit' + 1 , my_clicks = '$my_clicks' +1 WHERE username = '$pub'");
mysqli_query($con, "INSERT INTO credits_ VALUES('','$pub','1','1','$referer','$ip_address','$dates','$time','$ads_id','$username')");

}
else
{
//echo "high then three";
mysqli_query($con, "UPDATE soosal_ads SET clicks = '$clicks' +1   WHERE ads_id = '$ads_id'");
mysqli_query($con, "UPDATE soosal_ads SET my_clicks = '$my_clicks' +1 WHERE username = '$pub'");

}
}
//echo "url:$url <br/> referer:$referer <br/>refer: $referer_";
}
?>

CREATE TABLE `admin_data` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `running_ads` int(11) DEFAULT NULL,
  `approved_ads` int(11) DEFAULT NULL,
  `pending_ads` int(11) DEFAULT NULL,
  `rejected_ads` int(11) DEFAULT NULL,
  `suspended_ads` int(11) DEFAULT NULL,
  `stopped_ads` int(11) DEFAULT NULL,
  `publishers` int(11) DEFAULT NULL,
  `advertisers` int(11) DEFAULT NULL,
  `visits` int(11) DEFAULT NULL,
  `clicks` int(11) DEFAULT NULL,
  `impression` int(11) DEFAULT NULL,
  `credits` int(11) DEFAULT NULL,
  `credits_earn` int(11) DEFAULT NULL,
  `credits_spent` int(11) DEFAULT NULL,
  `date` date DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;

CREATE TABLE `adv` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `ads_id` int(11) DEFAULT NULL,
  `username` varchar(255) DEFAULT NULL,
  `clicked_of` varchar(255) DEFAULT NULL,
  `impression` int(11) DEFAULT NULL,
  `pageviews` int(11) DEFAULT NULL,
  `referer` varchar(255) DEFAULT NULL,
  `ip_address` varchar(255) DEFAULT NULL,
  `time` time DEFAULT NULL,
  `date` date DEFAULT NULL,
  `website` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;

CREATE TABLE `ads_details` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `ads_name` varchar(255) DEFAULT NULL,
  `ads_url` varchar(255) DEFAULT NULL,
  `ads_type` varchar(255) DEFAULT NULL,
  `ads_category` varchar(255) DEFAULT NULL,
  `ads_banner` varchar(255) DEFAULT NULL,
  `ads_description` text,
  `username` varchar(255) DEFAULT NULL,
  `date` date DEFAULT NULL,
  `time` time DEFAULT NULL,
  `ip_address` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;

CREATE TABLE `approved_ads` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `ads_id` int(11) DEFAULT NULL,
  `banner` varchar(255) DEFAULT NULL,
  `ads_credit` int(11) DEFAULT NULL,
  `username` varchar(255) DEFAULT NULL,
  `date` date DEFAULT NULL,
  `url` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;

CREATE TABLE `credits_` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `username` varchar(255) DEFAULT NULL,
  `credit_earn` int(11) DEFAULT NULL,
  `credit_spent` int(11) DEFAULT NULL,
  `referer_address` varchar(255) DEFAULT NULL,
  `ip_address` varchar(255) DEFAULT NULL,
  `date` date DEFAULT NULL,
  `time` time DEFAULT NULL,
  `ads_id` int(11) DEFAULT NULL,
  `whose_ads` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;

CREATE TABLE `earn_points` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) DEFAULT NULL,
  `ads_id` int(11) DEFAULT NULL,
  `username` varchar(255) DEFAULT NULL,
  `earn_points` int(11) DEFAULT NULL,
  `total_points` int(11) DEFAULT NULL,
  `date` date DEFAULT NULL,
  `time` time DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;

CREATE TABLE `final_data` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `username` varchar(255) DEFAULT NULL,
  `date` date DEFAULT NULL,
  `ads_id` int(11) DEFAULT NULL,
  `pageviews` int(11) DEFAULT NULL,
  `clicks` int(11) DEFAULT NULL,
  `clicks_sent` int(11) DEFAULT NULL,
  `visits` int(11) DEFAULT NULL,
  `credits` int(11) DEFAULT NULL,
  `credits_earned` int(11) DEFAULT NULL,
  `my_ads` int(11) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;

CREATE TABLE `members` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `username` varchar(255) DEFAULT NULL,
  `requestdomain` varchar(255) DEFAULT NULL,
  `presentdomain` varchar(255) DEFAULT NULL,
  `lastname` varchar(255) DEFAULT NULL,
  `firstname` varchar(255) DEFAULT NULL,
  `pending` int(11) DEFAULT NULL,
  `approved` int(11) DEFAULT NULL,
  `reject` int(11) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;

CREATE TABLE `only_clicks` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `ads_id` int(11) DEFAULT NULL,
  `username` varchar(255) DEFAULT NULL,
  `clicks` int(11) DEFAULT NULL,
  `clicked_from` varchar(255) DEFAULT NULL,
  `website` varchar(255) DEFAULT NULL,
  `ip_address` varchar(255) DEFAULT NULL,
  `time` time DEFAULT NULL,
  `date` date DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;

CREATE TABLE `publishers` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `username` varchar(255) DEFAULT NULL,
  `website` varchar(255) DEFAULT NULL,
  `date` date DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;

CREATE TABLE `soosal_ads` (
  `ads_id` int(11) NOT NULL AUTO_INCREMENT,
  `banner` varchar(255) DEFAULT NULL,
  `url` varchar(255) DEFAULT NULL,
  `impression` int(11) DEFAULT NULL,
  `my_impression` int(11) DEFAULT NULL,
  `clicks` int(11) DEFAULT NULL,
  `my_clicks` int(11) DEFAULT NULL,
  `pageviews` int(11) DEFAULT NULL,
  `ads_credit` int(11) DEFAULT NULL,
  `username` varchar(255) DEFAULT NULL,
  `date` date DEFAULT NULL,
  `status` int(11) DEFAULT NULL,
  PRIMARY KEY (`ads_id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;

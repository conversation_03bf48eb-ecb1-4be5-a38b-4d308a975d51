#header
{
background-color:purple;
color:white;
height:60px;
font-size:40px;
text-align:left;
font-family:impact;
font-style:italic;
border-radius:4px;
}
a:link ,a:visited
{
text-decoration:none;
color:green;
}
a:hover  ,a:active
{
color:white;

}
#menu{

height:30px;
margin-top:3px;
padding-bottom:10px;
}
ul
{
list-style-type:none;
background-color:purple;
border-radius:5px;
height:30px;
padding-top:9px;
margin-top:0.30%;
}

#hw
{
background-color:green;
color:white;
text-align:center;
margin-left:-0%;
width:300px;
border-radius:6px;
border-style:inset;
margin-top:-10%;
}
#som
{
background-color:purple;
width:1000px;
height:500px;
color:white;
margin-left:9%;
border-radius:12px;
}

input
{
font-style:italic;
width:260px;
border-radius:4px;
text-align:center;

}
input.reg
{
background-image:url(../../images/submit.jpg);
font-style:italic;
width:260px;
border-radius:4px;
text-align:center;
color:white;
font-weight:bold;
cursor:pointer;
font-size:15px;
}
input.reg:hover
{
background-image:url(../../images/submit1.jpg);
font-style:italic;
width:260px;
border-radius:4px;
text-align:center;
color:white;
font-weight:bold;
cursor:pointer;
font-size:15px;
}

caption{
background-color:white;
color:purple;
width:370px;
margin-left:2%;
border-radius:4px;
font-family:impact;
}
#error
{
background-color:red;
color:white;
text-align:center;
font-family:impact;
font-size:25px;
}#sucess
{
background-color:green;
color:white;
text-align:center;
font-family:impact;
font-size:25px;
}
h1
{
text-align:center;
font-family:verdana;
background-image:url(../../images/one.jpg);
color:purple;
text-transform:uppercase;
border-top-left-radius:11px;
border-top-right-radius:11px;
}



body
{
background-color:#EAEAEA;
}

#updates
{
text-align:left;

}
a:link.updates
{
color:green;
}
#sidebar{
margin-left:45%;
margin-top:-40%;
}
#upd
{

margin-left:35%;
margin-top:-30%;
}
td.words
{
background-color:orange;
color:white;
font-size:20px;
border-radius:5px;
text-align:center;
border-right-style:inset;
border-right-color:white;
border-bottom-style:inset;
border-bottom-color:white;
}
.hint
{
font-size:20px;
width:350px;
background-color:red;
float:left;
text-align:center;
border-right-style:inset;
border-right-color:white;
border-bottom-style:inset;
border-bottom-color:white;
margin-left:3%;
margin-top:5%;
opacity:0.8;
}
textarea
{
border-radius:5px;
}
select
{
border-radius:15px;
background-color:purple;
color:white;
cursor:pointer;
}option
{
border-radius:15px;
border-radius:15px;
background-color:purple;
color:white;
cursor:pointer;
}
.tables
{
margin-left:150px;
float:left;
}
.error
{
font-size:20px;
background-color:red;
color:white;
width:300px;
text-align:center;
margin-left:35%;
border-radius:5px;
}.ok
{
background-color:green;
color:white;
width:300px;
text-align:center;
margin-left:35%;
border-right-style:inset;
border-right-color:white;
border-bottom-style:inset;
border-bottom-color:white;
}
p.hints
{
background-color:green;
margin-top:-6.50%;
}
.close
{
margin-top:0%;
margin-left:93%;
cursor:pointer;
}
.close:hover
{
color:red;
}